#!/usr/bin/env python3
"""
参考任务决策执行Agent

融合决策和执行的Agent，直接调用vision模型输出坐标并执行Android操作
在prompt中包含参考任务的历史和图片
"""

import json
import re
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
from typing import Dict, Any, Tuple

from langchain.output_parsers import OutputFixingParser
from langchain_core.output_parsers import PydanticOutputParser
from loguru import logger
from pydantic import BaseModel, Field

from src.domain.reference_task.prompt.reference_decision_prompt import build_three_step_reference_decision_prompt, \
    system_invoke_prompt
from src.domain.ui_task.mobile.android.action_tool import (
    execute_simple_action
)
from src.domain.ui_task.mobile.android.image_processor import image_annotator
from src.domain.ui_task.mobile.android.screenshot_manager import convert_screenshot_to_base64, screenshot_manager
from src.domain.ui_task.mobile.repo.do.State import DeploymentState
from src.infra.model import get_chat_model, create_chat_completion


class InvalidStepIndexError(Exception):
    """当current_step_index不是有效数字时抛出的异常"""
    pass


class ReferenceDecisionResponse(BaseModel):
    """参考任务决策Agent响应的数据模型"""
    interface_analysis: str = Field(default="", description="当前界面分析")
    current_step_name: str = Field(default="", description="当前正在执行的步骤名称（包含序号），从测试用例信息获取")
    action_decision: str = Field(default="", description="动作决策结果")
    action: str = Field(default="", description="具体动作命令")


class ReferenceDecisionAgent:
    """参考任务决策执行Agent - 融合决策和执行"""

    def __init__(self):
        """初始化决策执行Agent"""
        # 创建JSON解析器
        self.pydantic_parser = PydanticOutputParser(pydantic_object=ReferenceDecisionResponse)

        # 尝试使用专门的修复模型，如果不存在则使用默认模型
        self.output_parser = OutputFixingParser.from_llm(
            parser=self.pydantic_parser,
            llm=get_chat_model(model_name="fix")
        )

        # 添加缓存机制，避免重复加载相同的截图
        self._screenshot_cache = {}
        self._cache_lock = threading.Lock()

    def _get_cached_screenshot(self, image_path: str, task_id: str) -> str:
        """
        获取缓存的截图base64数据

        Args:
            image_path: 图片路径
            task_id: 任务ID

        Returns:
            base64编码的图片数据
        """
        cache_key = f"{task_id}:{image_path}"

        with self._cache_lock:
            if cache_key in self._screenshot_cache:
                return self._screenshot_cache[cache_key]

        # 如果缓存中没有，则加载并缓存
        screenshot_base64 = convert_screenshot_to_base64(image_path, task_id)

        with self._cache_lock:
            # 限制缓存大小，避免内存过大
            if len(self._screenshot_cache) > 100:
                # 清理最旧的缓存项
                oldest_key = next(iter(self._screenshot_cache))
                del self._screenshot_cache[oldest_key]

            self._screenshot_cache[cache_key] = screenshot_base64

        return screenshot_base64

    def _get_all_screenshots_parallel(self, step_description: str,
                                      task_steps: list, current_step_index: int, state: DeploymentState) -> tuple:
        """
        并行获取成功案例截图数据

        Args:
            step_description: 当前步骤描述
            task_steps: 任务步骤列表
            current_step_index: 当前步骤索引
            state: 任务状态

        Returns:
            (step_screenshots, next_step_screenshot, execution_memory_screenshots) 元组
            注意：execution_memory_screenshots 现在总是返回空列表，因为在外部单独获取
        """
        # 并行获取成功案例截图数据
        reference_task_id = state.get("reference_task_id", "")
        step_screenshots = []
        next_step_screenshot = ""

        def get_step_screenshots():
            return self._get_step_reference_screenshots_optimized(reference_task_id, step_description,
                                                                  current_step_index)

        def get_next_step_screenshot():
            return self._get_next_step_first_screenshot_optimized(reference_task_id, task_steps, current_step_index)

        # 使用线程池并行执行
        with ThreadPoolExecutor(max_workers=2) as executor:
            future_to_task = {
                executor.submit(get_step_screenshots): 'step',
                executor.submit(get_next_step_screenshot): 'next'
            }

            for future in as_completed(future_to_task):
                task_type = future_to_task[future]
                try:
                    result = future.result()
                    if task_type == 'step':
                        step_screenshots = result
                    elif task_type == 'next':
                        next_step_screenshot = result
                except Exception as e:
                    logger.error(f"Error in parallel screenshot loading for {task_type}: {str(e)}")

        return step_screenshots, next_step_screenshot, []

    def execute_step_with_reference(
            self,
            state: DeploymentState,
            step_description: str,
            max_step_attempts: int = 7,
            max_exception_retries: int = 3
    ) -> str:
        """
        参照成功案例执行单个步骤，类似分步执行模式，具备异常重试能力

        Args:
            state: 参考任务状态
            step_description: 步骤描述
            max_step_attempts: 单个步骤最大尝试次数，默认7次
            max_exception_retries: 异常重试最大次数，默认3次

        Returns:
            "finished": 步骤完成
            "failed": 步骤失败
            "continue": 继续执行
        """
        task_id = state["task_id"]
        device_id = state["device"]

        # 检查当前步骤的执行次数
        current_step_index = state.get("current_step_index", 0)
        step_attempt_count = self._get_step_attempt_count(state, current_step_index)

        # 计算动态最大执行次数：成功案例轮数 + 7次
        reference_case_count = self._get_reference_case_count(state, step_description)
        dynamic_max_attempts = reference_case_count + 7

        # 检查当前步骤是否主要包含滑动操作，如果是则增加一倍执行次数
        is_scroll_heavy_step = self._is_scroll_heavy_step(state, current_step_index)
        if is_scroll_heavy_step:
            dynamic_max_attempts = dynamic_max_attempts * 2
            logger.info(f"[{task_id}] 📱 检测到滑动密集步骤，执行次数翻倍: {dynamic_max_attempts}")

        actual_max_attempts = max(dynamic_max_attempts, max_step_attempts)  # 至少保证原有的最大次数
        logger.info(f"[{task_id}] 🔄 Step attempt {step_attempt_count + 1}/{actual_max_attempts}")

        if step_attempt_count > actual_max_attempts:
            scroll_info = "，滑动密集步骤已翻倍" if is_scroll_heavy_step else ""
            error_msg = f"步骤执行次数超过最大限制({actual_max_attempts}次，成功案例{reference_case_count}轮+7次{scroll_info})，主动退出"
            logger.error(f"[{task_id}] ❌ {error_msg}")
            self._record_step_execution_history_with_index(state, step_description, error_msg, "failed", "failed",
                                                           "failed", current_step_index)
            return "failed"

        # 异常重试机制
        last_error_message = None
        for retry_attempt in range(max_exception_retries + 1):
            try:
                if retry_attempt > 0:
                    logger.info(f"[{task_id}] 🔄 Exception retry attempt {retry_attempt}/{max_exception_retries}")

                return self._execute_step_core_logic(state, step_description, device_id, task_id, last_error_message)

            except Exception as e:
                last_error_message = str(e)
                logger.error(
                    f"[{task_id}] ❌ Reference decision-execution agent failed (attempt {retry_attempt + 1}/{max_exception_retries + 1}): {last_error_message}")

                # 如果是最后一次重试，返回错误
                if retry_attempt >= max_exception_retries:
                    logger.error(f"[{task_id}] ❌ All exception retries exhausted, returning error")
                    return "error"

                # 否则继续重试，添加短暂延迟
                time.sleep(0.5)
                continue

    def _execute_step_core_logic(self, state: DeploymentState, step_description: str, device_id: str,
                                 task_id: str, last_error_message: str = None) -> str:
        """
        执行步骤的核心逻辑，从原来的try块中提取出来

        Args:
            state: 参考任务状态
            step_description: 步骤描述
            device_id: 设备ID
            task_id: 任务ID
            last_error_message: 上次执行的错误信息，用于重试时提供给模型

        Returns:
            "finished": 步骤完成
            "failed": 步骤失败
            "continue": 继续执行
        """
        start_time = time.time()
        logger.info(f"[{task_id}] 🧠 Reference decision-execution agent analyzing step: {step_description}")
        # 获取当前步骤的等待时间
        current_step_index = state.get("current_step_index", 0)
        step_wait_times = state.get("step_wait_times", [])
        print("step_wait_times", step_wait_times)
        wait_time = 1
        if current_step_index < len(step_wait_times):
            wait_time = step_wait_times[current_step_index]

        logger.info(
            f"[{task_id}] ⏱️ Waiting {wait_time}s before taking screenshot for step {current_step_index + 1}")
        time.sleep(wait_time)

        # 在拼接prompt前重新截图以获取最新界面状态
        logger.info(f"[{task_id}] 📸 Taking fresh screenshot before decision...")
        screenshot_path = screenshot_manager.take_screenshot(
            device=device_id,
            task_id=task_id,
            execution_count=state.get("execution_count", 0) + 1
        )
        # 转换截图为base64
        current_image_base64 = convert_screenshot_to_base64(screenshot_path, task_id)

        # 构建包含参考任务信息和当前步骤的prompt
        messages, thinking_for_output = self._build_step_reference_messages(state, current_image_base64,
                                                                            step_description,
                                                                            last_error_message)

        model_thinking_start_time = time.time()
        logger.info("thinking: " + thinking_for_output)
        chat_completion = create_chat_completion(
            model_name="reference",
            messages=messages,
            thinking=thinking_for_output,
            stream=False
        )
        model_response = chat_completion.choices[0].message.content

        model_thinking_time_time = time.time()

        # 计算模型思考时间
        model_thinking_time = model_thinking_time_time - model_thinking_start_time

        logger.info(f"[{task_id}] 🧠 Reference model response time: {model_thinking_time:.2f}s")
        logger.info(f"[{task_id}] 🧠 Reference model response: \n{model_response}")

        # 解析JSON响应
        parsed_fields, action_command = self._parse_json_response(model_response, task_id, state)

        # 获取模型决定的当前步骤名称（优先使用完整的步骤名称）
        current_step_name = parsed_fields.get("current_step_name_full",
                                              parsed_fields.get("current_step_name", step_description))

        # 直接创建包含完整数据的动作记录，避免创建后立即更新的性能问题
        current_action = self._create_complete_action_record(
            task_id, current_step_name, start_time, parsed_fields, action_command, screenshot_path
        )

        # 记录决策日志
        self._log_decision_immediately(parsed_fields, action_command, task_id)

        # 保存当前步骤索引，用于记录执行历史
        execution_step_index = state.get("current_step_index", 0)

        # 检查是否是finished或failed状态
        if action_command.strip() == "finished()" or action_command.strip().startswith("finished("):
            logger.info(f"[{task_id}] ✅ AI判断任务已完成")

            # 对于finished动作，完成action记录（因为不执行ADB命令）
            if current_action:
                finished_result = {"status": "success", "action": "finished", "message": "Task completed successfully"}
                self._complete_action_record(current_action, finished_result)

            # 更新state中的execution_count
            current_execution_count = state.get("execution_count", 0) + 1
            state["execution_count"] = current_execution_count

            # 记录成功完成的步骤执行历史
            accurate_step_name = parsed_fields.get("current_step_name", step_description)
            self._record_step_execution_history_with_index(state, accurate_step_name, model_response,
                                                           action_command, "finished",
                                                           screenshot_path, execution_step_index)
            return "finished"
        elif action_command.strip().startswith("failed("):
            logger.error(f"[{task_id}] ❌ Step marked as failed by AI")

            # 对于failed动作，完成action记录（因为不执行ADB命令）
            if current_action:
                # 提取失败原因
                reason = "Unknown error"
                if "content=" in action_command:
                    import re
                    content_match = re.search(r"content='([^']*)'", action_command)
                    if content_match:
                        reason = content_match.group(1)
                failed_result = {"status": "failed", "action": "failed", "message": f"Task failed: {reason}"}
                self._complete_action_record(current_action, failed_result)

            # 更新state中的execution_count
            current_execution_count = state.get("execution_count", 0) + 1
            state["execution_count"] = current_execution_count

            # 记录失败的步骤执行历史
            accurate_step_name = parsed_fields.get("current_step_name", step_description)
            self._record_step_execution_history_with_index(state, accurate_step_name, model_response,
                                                           action_command, "failed",
                                                           screenshot_path, execution_step_index)
            return "failed"

        # 检查是否需要切换步骤（根据current_step_name）- 只在非finished/failed状态下执行
        current_step_name = parsed_fields.get("current_step_name", "")
        if current_step_name:
            step_switched = self._check_and_switch_step(state, current_step_name, task_id)
            if step_switched:
                # 如果发生了步骤切换，更新执行步骤索引为新的步骤索引
                execution_step_index = state.get("current_step_index", 0)
                logger.info(
                    f"[{task_id}] 🔄 Step switched to: {current_step_name}, updated execution_step_index to {execution_step_index}")

        # 执行动作
        execution_result = self._execute_action_command(action_command, device_id, task_id, state)

        # 完成action记录（更新状态和结束时间）
        if current_action:
            self._complete_action_record(current_action, execution_result)

        # 执行完成后进行截图标注
        self._annotate_screenshot_after_execution(state, action_command, screenshot_path, execution_result)

        # 只有执行成功时才记录到状态历史和步骤执行历史
        if execution_result.get("status") == "success":
            # 更新state中的execution_count
            current_execution_count = state.get("execution_count", 0) + 1
            state["execution_count"] = current_execution_count

            self._record_execution_result(state, parsed_fields, execution_result, screenshot_path)
            # 记录成功的步骤执行历史，使用执行时的步骤索引和准确的步骤名称
            accurate_step_name = parsed_fields.get("current_step_name", step_description)
            self._record_step_execution_history_with_index(state, accurate_step_name, model_response,
                                                           action_command, "continue",
                                                           screenshot_path, execution_step_index)

            logger.info(
                f"[{task_id}] ✅ Execution result recorded to history, execution_count updated to {current_execution_count}")
            return "continue"
        else:
            logger.warning(
                f"[{task_id}] ⚠️ Execution failed (likely due to action parsing error), not recording to history to avoid contaminating next round")

            return "continue"

    @staticmethod
    def _preprocess_json_string(json_str: str) -> str:
        """
        预处理JSON字符串，修复常见的转义字符问题

        Args:
            json_str: 原始JSON字符串

        Returns:
            修复后的JSON字符串
        """
        # 修复无效的单引号转义 \' -> '
        # 在JSON中，单引号不需要转义，只有双引号需要转义
        cleaned = json_str.replace("\\'", "'")

        # 其他可能的修复...
        # 如果需要，可以在这里添加更多的修复逻辑

        return cleaned

    def _build_step_reference_messages(
            self,
            state: DeploymentState,
            current_image_base64: str,
            step_description: str,
            last_error_message: str = None
    ):
        """
        构建包含参考任务信息和当前步骤的消息列表，用于分步执行

        Args:
            state: 参考任务状态
            current_image_base64: 当前截图base64
            step_description: 当前步骤描述
            last_error_message: 上次执行的错误信息，用于重试时提供给模型

        Returns:
            消息列表
        """
        # 构建系统prompt，使用新的三步骤逻辑
        # 使用state中当前的步骤信息，而不是传入的step_description参数
        current_step_index = state.get("current_step_index", 0)
        task_steps = state.get("task_steps", [])
        current_step_description = task_steps[current_step_index] if current_step_index < len(
            task_steps) else step_description

        # 获取当前步骤执行轮数和成功案例轮数
        # 对于思考模式判断，使用原始执行轮数，不进行滑动调整
        current_step_attempt_count = self._get_step_attempt_count_raw(state, current_step_index)
        reference_case_count = self._get_reference_case_count(state, current_step_description)

        # 新逻辑：如果当前步骤执行轮数 > 成功案例轮数，开启思考模式并追加成功案例截图
        should_enable_thinking = current_step_attempt_count > reference_case_count + 2

        # 记录决策日志
        task_id = state.get("task_id", "")
        logger.info(f"[{task_id}] 🧠 思考模式决策: 当前步骤执行轮数={current_step_attempt_count}, "
                    f"成功案例轮数={reference_case_count}, 开启思考模式={should_enable_thinking}")

        # 根据新逻辑决定是否开启思考模式
        if should_enable_thinking:
            thinking_for_output = "enabled"
        else:
            thinking_for_output = "disable"
        is_scroll_step = ('滑动' in current_step_description or '滚动' in current_step_description or
                          '拖动' in current_step_description or '拖拽' in current_step_description)
        thinking_for_output = "enabled" if is_scroll_step else thinking_for_output

        system_prompt = build_three_step_reference_decision_prompt(state, current_step_description,
                                                                   should_enable_thinking or is_scroll_step)
        messages = [{
            "role": "system",
            "content": system_prompt
        }, {
            "role": "system",
            "content": system_invoke_prompt()
        }]

        success_cases = []
        # 总是获取执行记忆截图，不管是否开启思考模式
        execution_memory_screenshots = self._get_execution_memory_screenshots_optimized(state)

        if should_enable_thinking:
            step_screenshots, next_step_screenshot, _ = self._get_all_screenshots_parallel(
                current_step_description, task_steps, current_step_index, state
            )
            if step_screenshots:
                success_cases = [{
                    "type": "text",
                    "text": f'''########## 成功案例截图 ##########
**参考成功案例截图**：
1.**逐轮对比分析**
   - 对比成功案例各轮截图的界面变化，理解正确的执行路径
2.**执行记忆回顾**
   - 回顾<执行记忆>,找到下一步要参考的成功案例截图
3.**元素精确定位**
   - 参考<成功案例截图>中目标元素的具体位置、颜色、文字内容，在<当前轮截图>中寻找相同或相似的元素
4.**路径纠正策略**
   - 如果界面基本正确但元素位置不同，调整点击坐标，以<当前轮截图>为准生成坐标
   - 如果有弹窗或遮挡，先处理这些干扰因素
5.**操作顺序复现**
   - 严格按照成功案例的操作顺序执行，每次操作后检查界面变化是否符合预期，如果某步操作效果与成功案例不同，立即停止并重新分析
'''
                }]
                for i, screenshot_base64 in enumerate(step_screenshots, 1):
                    success_cases.extend([
                        {
                            "type": "text",
                            "text": f"成功案例第{i}轮截图"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{screenshot_base64}",
                                "detail": "high"
                            }
                        }
                    ])

            if next_step_screenshot:
                next_step_count = len(step_screenshots) + 1

                success_cases.extend([
                    {
                        "type": "text",
                        "text": f"成功案例第{next_step_count}轮截图"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{next_step_screenshot}",
                            "detail": "high"
                        }
                    }
                ])

            if success_cases:
                messages.append({
                    "role": "user",
                    "content": success_cases
                })

        if execution_memory_screenshots:
            content_items = [
                {
                    "type": "text",
                    "text": "########## 执行记忆界面截图 ##########"
                }
            ]
            for i, (screenshot_base64, execution_count) in enumerate(execution_memory_screenshots):
                round_number = execution_count if execution_count else i + 1
                content_items.extend([
                    {
                        "type": "text",
                        "text": f"执行记忆第{round_number}轮截图"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{screenshot_base64}",
                            "detail": "high"
                        }
                    }
                ])

            messages.append({
                "role": "user",
                "content": content_items
            })

        expected_result_images = self._get_overall_expected_images(state)
        if expected_result_images:
            messages.append({
                "role": "user",
                "content": expected_result_images
            })

        messages.append({
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": "########## 当前轮截图 ##########"
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{current_image_base64}",
                        "detail": "high"
                    }
                }
            ]
        })

        if is_scroll_step:
            messages.append({
                "role": "user",
                "content": f"""########## 滑动重要提示 ##########
        - 当前正在执行步骤包含了滑动操作，不要被<执行记忆>带偏了一个劲朝着一个方向滑,你必须按照以下要求执行：
        1.我让给你回顾最近2轮<执行记忆>和<执行记忆界面截图>，你总是不回顾，执行了四五轮了才想起来滑到尽头了，你怎么就不对比图片是否变化了呢？？？
        2.回顾<执行记忆>,如果当前执行步骤执行记忆中已经切换过滑动方向，则继续沿用该方向滑动，禁止切换方向
        3.回顾<执行记忆界面截图>内容，将<执行记忆界面截图>和<当前轮截图>滑动起点元素进行比对，滑动起点元素的位置未发生变化，则说明已经滑动到尽头，立即切换到反方向继续滑动"""
            })

        if last_error_message:
            error_correction_message = f"""########## 重要提示：上次执行出现错误，请修正 ##########
上次执行错误信息：{last_error_message}
**特别注意**：
- 如果错误提示current_step_name格式不正确，请确保current_step_name字段严格按照<测试用例信息>中的步骤格式输出
- current_step_name必须包含步骤序号，格式如：1.点击登录按钮、2.输入用户名等
- 不能输出没有序号的步骤名称，如"参考全局移动路径，使用方向键移动红色X到达绿色终点、点击登录按钮、输入用户名等"
- 请从<测试用例信息>中选择正确的步骤名称，包含完整的序号和描述
请重新分析并输出正确格式的JSON响应。"""

            messages.append({
                "role": "user",
                "content": error_correction_message
            })

        return messages, thinking_for_output

    def _get_overall_expected_images(self, state: DeploymentState) -> list:
        """
        获取整个任务的期望结果图片列表，用于消息拼接

        Args:
            state: 参考任务状态

        Returns:
            期望结果图片的消息内容列表
        """
        try:
            overall_expected_result = state.get("overall_expected_result", {})
            if not overall_expected_result:
                return []

            # 获取期望结果图片列表
            expected_images = overall_expected_result.get("images", [])
            if not expected_images:
                return []

            # 构建期望结果图片的消息内容
            content_items = [
                {
                    "type": "text",
                    "text": "########## 预期结果图片列表 ##########"
                }
            ]

            for i, image_info in enumerate(expected_images, 1):
                image_name = image_info.get("image_name", f"expected_image_{i}")
                image_path = image_info.get("image_path", "")

                if not image_path:
                    continue

                try:
                    # 读取图片文件并转换为base64
                    image_base64 = convert_screenshot_to_base64(image_path, state.get("task_id", ""))
                    if image_base64:
                        content_items.extend([
                            {
                                "type": "text",
                                "text": f"第{i}张预期结果图片：{image_name}"
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{image_base64}",
                                    "detail": "high"
                                }
                            }
                        ])
                except Exception as e:
                    task_id = state.get("task_id", "")
                    logger.warning(f"[{task_id}] ⚠️ Failed to load expected result image {image_path}: {str(e)}")
                    continue

            # 如果没有成功加载任何图片，返回空列表
            if len(content_items) <= 1:
                return []

            return content_items

        except Exception as e:
            task_id = state.get("task_id", "")
            logger.error(f"[{task_id}] ❌ Error getting overall expected images: {str(e)}")
            return []

    def _parse_json_response(self, model_response: str, task_id: str, state: DeploymentState = None) -> Tuple[
        Dict[str, Any], str]:
        """
        解析模型的JSON响应，提取各个字段
        先尝试直接解析JSON，失败则用大模型格式化，字段解析失败直接抛出异常重试

        Args:
            model_response: 模型的完整响应
            task_id: 任务ID
            state: 任务状态，用于获取步骤列表和替换步骤名称

        Returns:
            Tuple[parsed_fields, action_command]: 解析的字段字典、动作命令

        Raises:
            Exception: 当JSON解析失败或字段格式不正确时抛出异常
        """
        # 第一步：尝试直接解析JSON
        try:
            json_data = json.loads(model_response.strip())
            logger.info(f"[{task_id}] ✅ Direct JSON parsing successful")
        except json.JSONDecodeError as e:
            logger.warning(f"[{task_id}] ⚠️ Direct JSON parsing failed: {str(e)}")
            # 第二步：使用大模型格式化
            try:
                parsed_response: ReferenceDecisionResponse = self.output_parser.parse(model_response)
                json_data = parsed_response.model_dump()
                logger.info(f"[{task_id}] ✅ JSON format fixed successfully using OutputFixingParser")
            except Exception as fix_error:
                logger.error(f"[{task_id}] ❌ OutputFixingParser failed: {str(fix_error)}")
                logger.error(f"[{task_id}] Raw response: {model_response}")
                raise fix_error

        # 第三步：解析和验证字段，失败直接抛出异常
        current_step_name_raw = json_data.get("current_step_name", "")
        if not current_step_name_raw:
            raise InvalidStepIndexError("current_step_name is empty or missing")

        # 提取步骤序号和名称
        import re
        step_pattern = r'^(\d+)[.)、\-\s）]+(.*)$'
        match = re.match(step_pattern, current_step_name_raw.strip())

        if not match:
            raise InvalidStepIndexError(
                f"current_step_name does not contain valid step index format: '{current_step_name_raw}'")

        try:
            current_step_index_extracted = int(match.group(1))
            current_step_name_clean = match.group(2).strip()
        except (ValueError, IndexError) as e:
            raise InvalidStepIndexError(
                f"Failed to extract valid step index from current_step_name: {current_step_name_raw}")

        if current_step_index_extracted <= 0:
            raise InvalidStepIndexError(f"Extracted step index must be positive: {current_step_index_extracted}")

        # 根据步骤索引替换步骤名称（如果有state）
        current_step_name_full = current_step_name_raw  # 保存完整的步骤名称（包含序号）
        if state:
            task_steps = state.get("task_steps", [])
            array_index = current_step_index_extracted - 1
            if 0 <= array_index < len(task_steps):
                accurate_step_name = task_steps[array_index]
                current_step_name_full = accurate_step_name  # 使用完整的步骤名称
                accurate_step_match = re.match(step_pattern, accurate_step_name.strip())
                if accurate_step_match:
                    current_step_name_clean = accurate_step_match.group(2).strip()
                else:
                    current_step_name_clean = accurate_step_name.strip()
                # 更新状态中的当前步骤索引
                state["current_step_index"] = array_index

        # 构建返回的字段字典
        parsed_fields = {
            "interface_analysis": json_data.get("interface_analysis", ""),
            "current_step_index": current_step_index_extracted,
            "current_step_name": current_step_name_clean,
            "current_step_name_full": current_step_name_full,  # 新增：完整的步骤名称（包含序号）
            "action_decision": json_data.get("action_decision", ""),
            "action": json_data.get("action", "")
        }

        action_command = json_data.get("action", "")
        return parsed_fields, action_command

    def _get_step_reference_screenshots_optimized(self, reference_task_id: str, step_description: str,
                                                  current_step_index: int) -> list:
        """
        优化版本：获取参考任务中当前步骤的所有轮截图，使用缓存和并行处理
        改为按顺序匹配，不再依赖步骤名称匹配

        Args:
            reference_task_id: 参考任务ID
            step_description: 当前步骤描述（用于日志，实际匹配按顺序）
            current_step_index: 当前步骤索引

        Returns:
            当前步骤的截图base64列表
        """
        try:
            # 获取参考任务的动作记录
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
            actions = task_persistence_service.get_task_actions(reference_task_id)

            if not actions:
                logger.info(f"No actions found for reference task {reference_task_id}")
                return []

            # 按顺序匹配：根据当前步骤索引获取对应的成功案例动作
            step_actions = self._get_actions_by_step_index(actions, current_step_index)

            if not step_actions:
                logger.info(f"No matching actions found for step index {current_step_index}")
                return []

            # 使用线程池并行处理截图转换
            images = []
            with ThreadPoolExecutor(max_workers=5) as executor:
                future_to_action = {
                    executor.submit(self._get_cached_screenshot, action.image_path, reference_task_id): action
                    for action in step_actions
                }

                for future in as_completed(future_to_action):
                    try:
                        img_base64 = future.result()
                        if img_base64:
                            images.append(img_base64)
                    except Exception as e:
                        action = future_to_action[future]
                        logger.warning(f"Failed to load step reference image {action.image_path}: {str(e)}")
                        continue

            logger.info(
                f"Loaded {len(images)} step reference images for step index {current_step_index} by order matching in task {reference_task_id}")
            return images

        except Exception as e:
            logger.error(f"Error loading step reference screenshots: {str(e)}")
            return []

    def _get_actions_by_step_index(self, actions: list, current_step_index: int) -> list:
        """
        根据当前步骤索引获取对应的成功案例动作
        现在使用步骤序号进行精确匹配

        Args:
            actions: 所有动作记录列表
            current_step_index: 当前步骤索引（从0开始）

        Returns:
            当前步骤索引对应的动作列表
        """
        try:
            # 将步骤索引转换为步骤序号（从0开始转换为从1开始）
            target_step_number = current_step_index + 1

            # 直接按步骤序号筛选动作
            step_actions = []
            for action in actions:
                step_name = action.step_name
                if not step_name:
                    continue

                # 提取步骤序号
                step_number = self._extract_step_index_from_name(step_name)
                if step_number == target_step_number:
                    step_actions.append(action)

            if step_actions:
                logger.info(
                    f"Found {len(step_actions)} actions for step number {target_step_number} (index {current_step_index}) by step number matching")
            else:
                logger.info(f"No actions found for step number {target_step_number} (index {current_step_index})")

            return step_actions

        except Exception as e:
            logger.error(f"Error getting actions by step index: {str(e)}")
            return []

    def _extract_step_index_from_name(self, step_name: str) -> int | None:
        """
        从步骤名称中提取步骤序号

        Args:
            step_name: 步骤名称，如 "1.点击底部消息tab" 或 "2.点击页面右下角的"聊天"按钮"

        Returns:
            步骤序号（从1开始），如果无法提取则返回None
        """
        if not step_name:
            return None

        try:
            import re
            # 匹配步骤序号的正则表达式，支持多种格式
            # 匹配格式：数字. 数字) 数字、 数字- 数字空格 等
            step_pattern = r'^(\d+)[.)、\-\s）]+'
            match = re.match(step_pattern, step_name.strip())

            if match:
                step_index = int(match.group(1))
                return step_index
            else:
                return None

        except (ValueError, AttributeError) as e:
            logger.warning(f"Error extracting step index from '{step_name}': {str(e)}")
            return None

    def _get_next_step_first_screenshot_optimized(self, reference_task_id: str, task_steps: list,
                                                  current_step_index: int) -> str:
        """
        优化版本：获取成功案例下一步骤的第1轮截图，使用缓存
        改为按顺序匹配，不再依赖步骤名称匹配

        Args:
            reference_task_id: 参考任务ID
            task_steps: 任务步骤列表
            current_step_index: 当前步骤索引

        Returns:
            下一步骤的第一张截图base64，如果没有则返回空字符串
        """
        try:
            # 检查是否有下一步骤
            next_step_index = current_step_index + 1
            if next_step_index >= len(task_steps):
                logger.info("Already at the last step, no next step screenshot needed")
                return ""

            logger.info(f"Getting next step first screenshot for step index {next_step_index} by order matching")

            # 获取参考任务的动作记录
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
            actions = task_persistence_service.get_task_actions(reference_task_id)

            if not actions:
                logger.info(f"No actions found for reference task {reference_task_id}")
                return ""

            # 按顺序获取下一步骤的第一个动作
            next_step_actions = self._get_actions_by_step_index(actions, next_step_index)

            if next_step_actions:
                # 获取下一步骤的第一个动作的截图
                first_action = next_step_actions[0]
                screenshot_base64 = self._get_cached_screenshot(first_action.image_path, reference_task_id)

                if screenshot_base64:
                    logger.info(f"Successfully loaded next step first screenshot for step index {next_step_index}")
                    return screenshot_base64

            logger.info(f"No screenshot found for next step index {next_step_index}")
            return ""

        except Exception as e:
            logger.error(f"Error loading next step first screenshot: {str(e)}")
            return ""

    def _execute_action_command(self, action_command: str, device_id: str, task_id: str,
                                state: DeploymentState = None) -> Dict[str, Any]:
        """
        执行动作命令，参考execution_agent的实现方式

        Args:
            action_command: 动作命令
            device_id: 设备ID
            task_id: 任务ID

        Returns:
            执行结果
        """
        try:
            logger.info(f"[{task_id}] 🎯 Executing action command: {action_command}")

            # 处理特殊动作（finished, failed）
            if action_command.strip() == "finished()" or action_command.strip().startswith("finished("):
                return {
                    "status": "success",
                    "action": "finished",
                    "message": "Task completed successfully"
                }

            if action_command.strip().startswith("failed("):
                # 提取失败原因
                reason = "Unknown error"
                if "content=" in action_command:
                    content_match = re.search(r"content='([^']*)'", action_command)
                    if content_match:
                        reason = content_match.group(1)

                return {
                    "status": "failed",
                    "action": "failed",
                    "message": f"Task failed: {reason}"
                }

            # 处理wait动作
            if action_command.strip().startswith("wait("):
                seconds_match = re.search(r"wait\(seconds=(\d+)\)", action_command)
                if seconds_match:
                    seconds = int(seconds_match.group(1))
                else:
                    seconds = 3  # 默认等待3秒

                time.sleep(seconds)
                return {
                    "status": "success",
                    "action": f"wait({seconds})",
                    "message": f"Waited for {seconds} seconds"
                }

            # 处理output动作，支持动态字段存储
            if action_command.strip().startswith("output("):
                if state is not None:
                    return self._execute_output_with_state_reference(action_command, state, task_id)
                else:
                    # 回退到原有的简单处理逻辑
                    content = ""
                    if "content=" in action_command:
                        content_match = re.search(r"content='([^']*)'", action_command)
                        if not content_match:
                            content_match = re.search(r'content="([^"]*)"', action_command)
                        if content_match:
                            content = content_match.group(1)

                    logger.info(f"[{task_id}] 📝 Print action: {content}")
                    return {
                        "status": "success",
                        "action": "print",
                        "message": f"Print action completed: {content}",
                        "content": content
                    }

            # 直接使用action_tool执行动作（与execution_agent保持一致）
            logger.info(f"[{task_id}] ⚡ Executing action with action_tool: {action_command}")
            result = execute_simple_action(action_command, device_id)

            logger.info(f"[{task_id}] 🎯 Action execution result: {result}")

            return result

        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error executing action command: {str(e)}")
            return {
                "status": "error",
                "message": f"Execution error: {str(e)}",
                "action": action_command
            }

    def _execute_output_with_state_reference(self, action_command: str, state: DeploymentState, task_id: str) -> Dict[
        str, Any]:
        """
        执行output动作并支持动态字段存储到state中（参考任务版本）

        Args:
            action_command: output动作命令
            state: 当前状态
            task_id: 任务ID

        Returns:
            执行结果
        """
        from src.domain.ui_task.mobile.android.action_tool import ActionParser

        # 使用ActionParser解析output动作
        parser = ActionParser(state["device"], task_id)
        parsed_action = parser.parse(action_command)

        if not parsed_action or parsed_action.get("action") != "output":
            logger.warning(f"[{task_id}] ⚠️ Failed to parse output action: {action_command}")
            # 回退到原有的简单处理逻辑
            content = ""
            if "content=" in action_command:
                content_match = re.search(r"content='([^']*)'", action_command)
                if not content_match:
                    content_match = re.search(r'content="([^"]*)"', action_command)
                if content_match:
                    content = content_match.group(1)

            logger.info(f"[{task_id}] 📝 Output action: {content}")
            return {
                "status": "success",
                "action": "output",
                "message": f"Output action completed: {content}",
                "content": content
            }

        content = parsed_action.get("content", "")
        is_dynamic_field = parsed_action.get("is_dynamic_field", False)
        field_name = parsed_action.get("field_name")

        logger.info(f"[{task_id}] 📝 Output action: {content}")

        # 如果是动态字段，存储到state中
        if is_dynamic_field and field_name:
            # 初始化dynamic_fields字段（如果不存在）
            if "dynamic_fields" not in state or state["dynamic_fields"] is None:
                state["dynamic_fields"] = {}

            # 存储动态字段
            state["dynamic_fields"][field_name] = content
            logger.info(f"[{task_id}] 💾 Stored dynamic field: {field_name} = {content}")

            # 记录到历史中，方便调试
            state["history"].append({
                "action": "dynamic_field_storage",
                "field_name": field_name,
                "field_value": content,
                "timestamp": time.time()
            })

        return {
            "status": "success",
            "action": "output",
            "message": f"Output action completed: {content}",
            "content": content,
            "is_dynamic_field": is_dynamic_field,
            "field_name": field_name
        }

    @staticmethod
    def _log_decision_immediately(parsed_fields: Dict[str, Any], action_command: str, task_id: str):
        try:
            from src.domain.ui_task.mobile.service.async_log_service import async_log_service

            async_log_service.log_decision_async(task_id, parsed_fields, action_command)

        except Exception as e:
            logger.warning(f"[{task_id}] Failed to queue reference decision log: {str(e)}")

    @staticmethod
    def _create_complete_action_record(task_id: str, step_description: str, start_time: float,
                                       parsed_fields: dict, action_command: str, screenshot_path: str):
        """
        直接创建完整的动作记录，避免创建后立即更新的性能问题

        Args:
            task_id: 任务ID
            step_description: 步骤描述
            start_time: 开始时间（时间戳）
            parsed_fields: 解析的字段
            action_command: 动作命令
            screenshot_path: 截图路径

        Returns:
            创建的动作记录对象
        """
        try:
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
            from datetime import datetime

            # 构建完整的thought内容
            interface_analysis = parsed_fields.get("interface_analysis", "")
            action_decision = parsed_fields.get("action_decision", "")

            # 组合thought内容
            thought_content = ""
            if interface_analysis:
                thought_content += f"界面分析: {interface_analysis}\n"
            if action_decision:
                thought_content += f"决策内容: {action_decision}\n"
            if action_command:
                thought_content += f"执行动作: {action_command}"

            # 直接创建包含完整数据的动作记录
            current_action = task_persistence_service.create_complete_task_action(
                task_id=task_id,
                step_name=step_description,
                action_command=action_command,
                decision_content=thought_content.strip(),
                start_time=datetime.fromtimestamp(start_time),
                image_path=screenshot_path
            )

            return current_action

        except Exception as e:
            logger.error(f"[{task_id}] ❌ Failed to create complete action record: {str(e)}")
            return None

    @staticmethod
    def _complete_action_record(current_action, execution_result):
        """
        完成action记录，更新状态和结束时间

        Args:
            current_action: 动作记录对象
            execution_result: 执行结果
        """
        if not current_action:
            return

        try:
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service

            # 判断执行是否成功
            success = execution_result.get("status") == "success"
            error_message = execution_result.get("message") if not success else None

            # 完成action记录
            task_persistence_service.complete_task_action(
                action_id=current_action.id,
                success=success,
                error_message=error_message,
                before_screenshot=None,
                verification_result=None,
                final_action_command=None
            )

            status_text = "成功" if success else "失败"
            logger.debug(f"⏰ Reference action {current_action.id} completed: {status_text}")

        except Exception as e:
            logger.warning(f"Failed to complete reference action record: {str(e)}")

    @staticmethod
    def _update_action_end_time_after_execution_async(current_action):
        """
        异步更新动作执行结束时间

        Args:
            current_action: 动作记录对象
        """
        if not current_action:
            return

        try:
            from src.domain.ui_task.mobile.service.async_log_service import async_log_service
            from datetime import datetime

            # 创建异步任务来更新action结束时间
            update_task = {
                'type': 'update_action_end_time_by_id',
                'action_id': current_action.id,
                'end_time': datetime.now().isoformat(),
                'timestamp': datetime.now().isoformat()
            }

            async_log_service.log_queue.put_nowait(update_task)
            logger.debug(f"⏰ Reference action end_time update queued for action {current_action.id}")

        except Exception as e:
            logger.warning(f"Failed to queue reference action end_time update: {str(e)}")

    @staticmethod
    def _annotate_screenshot_after_execution(
            state: DeploymentState,
            action_command: str,
            screenshot_path: str,
            execution_result: Dict[str, Any]
    ) -> None:
        """
        在动作执行完成后对截图进行标注

        Args:
            state: 参考任务状态
            action_command: 动作命令
            screenshot_path: 截图路径
            execution_result: 执行结果
        """
        task_id = state["task_id"]
        device_id = state["device"]

        # 跳过不需要标注的动作
        if not ReferenceDecisionAgent._requires_coordinates(action_command):
            return

        # 只有在动作执行成功时才进行标注
        if execution_result.get("status") != "success":
            logger.info(f"[{task_id}] ⚠️ Skipping annotation due to failed execution: {execution_result.get('status')}")
            return

        try:
            # 执行异步标注（不阻塞主流程）
            image_annotator.annotate_screenshot_from_action_async(
                screenshot_path, action_command, task_id, device=device_id
            )
            logger.info(f"[{task_id}] 🎯 Screenshot annotation queued after reference execution: {action_command}")

        except Exception as e:
            logger.warning(f"[{task_id}] ⚠️ Failed to annotate screenshot after reference execution: {str(e)}")

    @staticmethod
    def _requires_coordinates(action_str: str) -> bool:
        """
        判断动作是否需要坐标（参考execution_agent的实现）

        Args:
            action_str: 动作字符串

        Returns:
            是否需要坐标
        """
        action_str_lower = action_str.lower()

        # 需要坐标的动作类型
        coordinate_actions = [
            'click', 'long_press', 'drag', 'scroll'
        ]

        # 不需要坐标的动作类型
        non_coordinate_actions = [
            'wait', 'back', 'type', 'delete', 'enter', 'finished', 'failed'
        ]

        # 检查是否包含需要坐标的动作
        for action in coordinate_actions:
            if action in action_str_lower:
                return True

        # 检查是否包含不需要坐标的动作
        for action in non_coordinate_actions:
            if action in action_str_lower:
                return False

        # 默认情况下，如果无法确定，返回False（不需要坐标）
        return False

    @staticmethod
    def _record_execution_result(
            state: DeploymentState,
            parsed_fields: Dict[str, Any],
            execution_result: Dict[str, Any],
            screenshot_path: str = None
    ):
        """
        记录执行结果到状态历史

        Args:
            state: 参考任务状态
            parsed_fields: 解析的字段
            execution_result: 执行结果
            screenshot_path: 截图路径
        """
        try:
            # 确保execution_count从1开始
            execution_count = state.get("execution_count", 0)
            if execution_count == 0:
                execution_count = 1

            # 将截图路径添加到执行结果中
            if screenshot_path:
                execution_result = execution_result.copy()
                execution_result["screenshot_path"] = screenshot_path

            history_entry = {
                "execution_count": execution_count,
                "parsed_fields": parsed_fields,
                "execution_result": execution_result,
                "timestamp": datetime.now().isoformat()
            }

            if "history" not in state:
                state["history"] = []

            state["history"].append(history_entry)
        except Exception as e:
            logger.error(f"Error recording execution result: {str(e)}")

    @staticmethod
    def _record_step_execution_history_with_index(
            state: DeploymentState,
            step_description: str,
            ai_response: str,
            action_command: str,
            step_status: str,
            screenshot_path: str = "",
            step_index: int = None
    ):
        """
        记录步骤执行历史，使用指定的步骤索引

        Args:
            state: 参考任务状态
            step_description: 步骤描述
            ai_response: AI响应内容
            action_command: 执行的动作命令
            step_status: 步骤状态
            screenshot_path: 截图路径
            step_index: 指定的步骤索引
        """
        from datetime import datetime

        if step_index is None:
            step_index = state.get("current_step_index", 0)

        # 使用state中的execution_count，确保与截图命名一致
        execution_count = state.get("execution_count", 1)

        history_entry = {
            "action": "step_execution_with_reference",
            "step_index": step_index,
            "step_description": step_description,
            "ai_response": ai_response,
            "action_command": action_command,
            "step_status": step_status,
            "screenshot_path": screenshot_path,
            "execution_count": execution_count,  # 添加执行轮次
            "timestamp": datetime.now().isoformat()
        }

        if "history" not in state:
            state["history"] = []
        state["history"].append(history_entry)

    @staticmethod
    def _escape_template_variables(text: str) -> str:
        """
        转义文本中的模板变量符号，防止LangChain将其识别为变量

        Args:
            text: 原始文本

        Returns:
            转义后的文本
        """
        if not text:
            return text

        # 将单个花括号转义为双花括号
        # 这样LangChain就不会将其识别为模板变量
        return text.replace("{", "{{").replace("}", "}}")

    @staticmethod
    def _check_and_switch_step(state: DeploymentState, current_step_name: str, task_id: str) -> bool:
        """
        检查并切换步骤，根据模型输出的current_step_name

        Args:
            state: 参考任务状态
            current_step_name: 模型输出的当前步骤名称
            task_id: 任务ID

        Returns:
            是否发生了步骤切换
        """
        try:
            task_steps = state.get("task_steps", [])
            current_step_index = state.get("current_step_index", 0)

            # 查找current_step_name在task_steps中的索引
            new_step_index = None
            for i, step in enumerate(task_steps):
                # 使用灵活的匹配逻辑
                if ReferenceDecisionAgent._is_step_name_match(step, current_step_name):
                    new_step_index = i
                    break

            if new_step_index is not None and new_step_index != current_step_index:
                # 检查是否是最后一步，如果是最后一步且要回退，则不允许
                if current_step_index == len(task_steps) - 1 and new_step_index < current_step_index:
                    logger.warning(
                        f"[{task_id}] ⚠️ Preventing step regression from final step {current_step_index} to {new_step_index}")
                    return False

                # 发生步骤切换
                logger.info(f"[{task_id}] 🔄 Step switching from index {current_step_index} to {new_step_index}")
                logger.info(
                    f"[{task_id}] 🔄 Step switching from '{task_steps[current_step_index] if current_step_index < len(task_steps) else 'unknown'}' to '{task_steps[new_step_index]}'")

                # 更新状态中的当前步骤索引
                state["current_step_index"] = new_step_index

                return True

            return False

        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error in step switching: {str(e)}")
            return False

    @staticmethod
    def _is_step_name_match(task_step: str, current_step_name: str) -> bool:
        """
        判断步骤名称是否匹配

        Args:
            task_step: 任务步骤（如 "1.点击底部消息tab"）
            current_step_name: 模型输出的步骤名称

        Returns:
            是否匹配
        """
        if not task_step or not current_step_name:
            return False

        # 清理字符串
        task_step_clean = task_step.strip()
        current_step_clean = current_step_name.strip()

        # 1. 完全匹配
        if task_step_clean == current_step_clean:
            return True

        # 2. 去除序号后匹配（只有当current_step没有序号时才允许）
        import re
        task_step_no_num = re.sub(r'^\d+[.)、\-\s]*', '', task_step_clean)
        current_step_no_num = re.sub(r'^\d+[.)、\-\s]*', '', current_step_clean)

        if task_step_no_num == current_step_no_num:
            # 检查current_step是否有序号
            current_step_has_num = bool(re.match(r'^\d+[.)、\-\s]', current_step_clean))

            # 如果current_step有序号，说明是不同序号的相同内容，不应该匹配
            if current_step_has_num:
                return False

            # 如果current_step没有序号，说明是去除序号后的匹配，可以匹配
            return True

        return False

    def _get_reference_case_count(self, state: DeploymentState, step_description: str) -> int:
        """
        获取指定步骤的成功案例轮数，使用与截图获取相同的逻辑

        Args:
            state: 参考任务状态
            step_description: 步骤描述

        Returns:
            该步骤的成功案例轮数
        """
        try:
            reference_task_id = state.get("reference_task_id", "")
            current_step_index = state.get("current_step_index", 0)

            if not reference_task_id:
                return 1

            # 首先尝试从数据库获取参考任务的动作记录
            actions = []
            try:
                from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
                actions = task_persistence_service.get_task_actions(reference_task_id)
            except Exception as db_error:
                logger.warning(f"Failed to get actions from database: {str(db_error)}")
                actions = []

            # 如果数据库获取失败，使用state中的reference_actions作为备用
            if not actions:
                logger.info(f"Using reference_actions from state as fallback")
                reference_actions = state.get("reference_actions", [])
                if reference_actions:
                    # 转换格式以匹配数据库格式
                    actions = []
                    for action in reference_actions:
                        # 创建一个简单的对象来模拟数据库记录
                        class MockAction:
                            def __init__(self, step_name):
                                self.step_name = step_name

                        actions.append(MockAction(action.get("step_name", "")))

            if not actions:
                logger.info(f"No actions found for reference task {reference_task_id}")
                return 1

            # 使用步骤序号匹配：根据当前步骤索引获取对应的成功案例动作
            step_actions = self._get_actions_by_step_index(actions, current_step_index)

            step_reference_count = len(step_actions)
            if step_reference_count > 0:
                logger.info(f"Found {step_reference_count} reference actions for step index {current_step_index}")
                return max(step_reference_count, 1)
            else:
                logger.info(f"No reference actions found for step index {current_step_index}")
                return 1

        except Exception as e:
            logger.error(f"Error getting reference case count: {str(e)}")
            return 1

    @staticmethod
    def _get_step_attempt_count(state: DeploymentState, step_index: int) -> int:
        """
        获取指定步骤的尝试次数，参照execution_agent的实现

        对于滑动密集步骤，两次滑动算作一次执行

        Args:
            state: 参考任务状态
            step_index: 步骤索引

        Returns:
            该步骤的尝试次数
        """
        history = state.get("history", [])

        # 统计当前步骤的执行记录数量
        step_attempts = [r for r in history if
                         r.get("action") == "step_execution_with_reference" and
                         r.get("step_index") == step_index]

        total_attempts = len(step_attempts)

        # 添加调试日志
        task_id = state.get("task_id", "")
        logger.info(f"[{task_id}] 📊 步骤{step_index}执行次数统计: 找到{total_attempts}条执行记录")
        for i, attempt in enumerate(step_attempts):
            step_desc = attempt.get("step_description", "")
            action_cmd = attempt.get("action_command", "")
            logger.info(f"[{task_id}] 📊 第{i + 1}条记录: {step_desc[:30]}... -> {action_cmd[:50]}...")

        # 检查是否为滑动密集步骤
        if ReferenceDecisionAgent._is_scroll_heavy_step(state, step_index):
            # 统计滑动操作的数量
            scroll_count = 0
            non_scroll_count = 0

            for attempt in step_attempts:
                action_command = attempt.get("action_command", "").lower()
                # 检查是否包含滑动相关的动作
                if any(scroll_keyword in action_command for scroll_keyword in ['scroll', 'swipe']):
                    scroll_count += 1
                else:
                    non_scroll_count += 1

            # 两次滑动算作一次执行，非滑动操作正常计算
            adjusted_scroll_count = (scroll_count + 1) // 2  # 向上取整
            adjusted_total = adjusted_scroll_count + non_scroll_count

            logger.debug(f"滑动密集步骤执行次数调整: 原始{total_attempts}次 -> 调整后{adjusted_total}次 "
                         f"(滑动{scroll_count}次->调整为{adjusted_scroll_count}次, 非滑动{non_scroll_count}次)")

            return adjusted_total

        return total_attempts

    @staticmethod
    def _get_step_attempt_count_raw(state: DeploymentState, step_index: int) -> int:
        """
        获取指定步骤的原始尝试次数，不进行滑动调整
        用于思考模式的判断逻辑

        Args:
            state: 参考任务状态
            step_index: 步骤索引

        Returns:
            该步骤的原始尝试次数
        """
        history = state.get("history", [])

        # 统计当前步骤的执行记录数量
        step_attempts = [r for r in history if
                         r.get("action") == "step_execution_with_reference" and
                         r.get("step_index") == step_index]

        total_attempts = len(step_attempts)

        # 添加调试日志
        task_id = state.get("task_id", "")
        logger.info(f"[{task_id}] 📊 步骤{step_index}原始执行次数: {total_attempts}轮")
        for i, attempt in enumerate(step_attempts):
            step_desc = attempt.get("step_description", "")
            action_cmd = attempt.get("action_command", "")
            logger.info(f"[{task_id}] 📊 第{i + 1}轮: {step_desc[:30]}... -> {action_cmd[:50]}...")

        return total_attempts

    @staticmethod
    def _is_scroll_heavy_step(state: DeploymentState, step_index: int) -> bool:
        """
        检测当前步骤是否为滑动密集步骤

        通过分析当前步骤的执行历史，如果滑动操作占比超过50%，则认为是滑动密集步骤

        Args:
            state: 参考任务状态
            step_index: 步骤索引

        Returns:
            是否为滑动密集步骤
        """
        history = state.get("history", [])

        # 获取当前步骤的所有执行记录
        step_attempts = [r for r in history if
                         r.get("action") == "step_execution_with_reference" and
                         r.get("step_index") == step_index]

        if not step_attempts:
            return False

        # 统计滑动操作的数量
        scroll_count = 0
        total_count = len(step_attempts)

        for attempt in step_attempts:
            action_command = attempt.get("action_command", "").lower()
            # 检查是否包含滑动相关的动作
            if any(scroll_keyword in action_command for scroll_keyword in ['scroll', 'swipe']):
                scroll_count += 1

        # 如果滑动操作占比超过50%，则认为是滑动密集步骤
        scroll_ratio = scroll_count / total_count if total_count > 0 else 0
        is_scroll_heavy = scroll_ratio > 0.5

        if is_scroll_heavy:
            logger.info(
                f"检测到滑动密集步骤 - 步骤索引: {step_index}, 滑动操作占比: {scroll_ratio:.2%} ({scroll_count}/{total_count})")

        return is_scroll_heavy

    def _get_execution_memory_screenshots_optimized(self, state: DeploymentState) -> list:
        """
        优化版本：获取执行记忆界面截图，使用缓存和并行处理
        修改为获取最近的所有执行截图，不限制于成功执行的记录

        Args:
            state: 参考任务状态

        Returns:
            (screenshot_base64, execution_count) 元组列表，最多返回2张最近的截图
        """
        task_id = state.get("task_id", "")
        try:
            history = state.get("history", [])

            # 获取所有包含截图路径的执行记录，按时间倒序排列
            execution_records = []
            for record in reversed(history):
                screenshot_path = None
                execution_count = None

                # 检查不同类型的记录
                if record.get("action") == "step_execution_with_reference":
                    # 步骤执行记录
                    screenshot_path = record.get("screenshot_path")
                    execution_count = record.get("execution_count", 1)
                elif record.get("execution_result") and record.get("execution_result", {}).get("screenshot_path"):
                    # 一般执行结果记录
                    screenshot_path = record.get("execution_result", {}).get("screenshot_path")
                    execution_count = record.get("execution_count", 1)

                if screenshot_path:
                    execution_records.append({
                        "screenshot_path": screenshot_path,
                        "execution_count": execution_count,
                        "timestamp": record.get("timestamp", "")
                    })

                    # 最多获取2张截图
                    if len(execution_records) >= 2:
                        break

            if not execution_records:
                logger.info(f"[{task_id}] 没有找到执行记忆截图，这可能是第一次执行")
                return []

            logger.info(f"[{task_id}] 找到 {len(execution_records)} 张执行记忆截图")

            # 使用线程池并行转换截图
            screenshots = []

            # 按时间正序排列（最早的在前）
            execution_records = list(reversed(execution_records))

            with ThreadPoolExecutor(max_workers=2) as executor:
                future_to_record = {
                    executor.submit(self._get_cached_screenshot, record.get("screenshot_path", ""), task_id): record
                    for record in execution_records
                }

                for future in as_completed(future_to_record):
                    record = future_to_record[future]
                    try:
                        screenshot_base64 = future.result()
                        if screenshot_base64:
                            execution_count = record.get("execution_count", 1)
                            screenshots.append((screenshot_base64, execution_count))
                            logger.debug(f"[{task_id}] 成功加载执行记忆截图，execution_count: {execution_count}")
                    except Exception as e:
                        screenshot_path = record.get("screenshot_path", "")
                        logger.warning(f"[{task_id}] 加载执行记忆截图失败 {screenshot_path}: {str(e)}")
                        continue

            logger.info(f"[{task_id}] 成功加载 {len(screenshots)} 张执行记忆截图")
            return screenshots

        except Exception as e:
            logger.error(f"[{task_id}] 获取执行记忆界面截图时出错: {str(e)}")
            return []
